<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

// Initialize models
$destinationModel = new Destination();
$tourPackageModel = new TourPackage();

// Fetch destinations from database (limit to 8 for display)
$destinations = $destinationModel->findAllWithImages();

// Ensure we always have exactly 8 cards
$displayDestinations = array_slice($destinations, 0, 8);

// Fill remaining slots with placeholder data if needed
while (count($displayDestinations) < 8) {
    $displayDestinations[] = [
        'destination_id' => null,
        'name' => 'Coming Soon',
        'short_description' => 'New exciting destination will be available soon. Stay tuned for amazing adventures!',
        'price' => 0,
        'display_image_url' => null,
        'created_by' => 'Meleva Tours'
    ];
}

// Fetch featured tour packages (limit to 7 for display)
$featuredPackages = $tourPackageModel->findFeaturedWithDetails(7);

// Create placeholder packages if we have fewer than 7 featured packages
$displayPackages = $featuredPackages;
while (count($displayPackages) < 7) {
    $displayPackages[] = [
        'tour_package_id' => null,
        'name' => 'Coming Soon',
        'description' => 'New exciting tour package will be available soon. Stay tuned for amazing adventures!',
        'price' => 0,
        'duration' => 'TBD',
        'display_image_url' => null,
        'type_name' => 'Adventure Tours'
    ];
}

// Set SEO data for homepage
$seoTitle = 'Meleva Tours & Travel | Authentic African Safari Adventures';
$seoDescription = 'Experience authentic African safari adventures with Meleva Tours & Travel. Discover Kenya\'s wildlife, stunning landscapes, and cultural heritage through our expertly crafted tour packages and destinations.';
$seoKeywords = 'Kenya safari, African tours, wildlife safari, Maasai Mara, Kilifi tours, Kenya travel, safari packages, African adventure, wildlife photography, cultural tours, Meleva Tours';
$seoImage = 'images/hero-bg.jpg';
$seoType = 'website';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="dist/tailwind.css">
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <style>
        /* ===================================
           INDEX PAGE SPECIFIC STYLES
           =================================== */

        .destination-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .destination-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-left-color: #f97316;
        }

        /* Custom navigation buttons for swiper */
        .swiper-button-next-custom,
        .swiper-button-prev-custom {
            width: 56px;
            height: 56px;
            background: #111827; /* bg-gray-900 */
            border-radius: 50%;
            border: none;
            outline: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            color: #f97316;
            transition: all 0.3s ease;
            transform-origin: center center;
        }

        .swiper-button-next-custom:hover,
        .swiper-button-prev-custom:hover {
            background: #1f2937; /* bg-gray-800 */
            color: #fb923c; /* text-orange-400 */
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Scale effect for navigation buttons without affecting position */
        .swiper-button-next-custom:hover svg,
        .swiper-button-prev-custom:hover svg {
            transform: scale(1.1);
        }

        /* Hide default Swiper navigation buttons */
        .swiper-button-next,
        .swiper-button-prev {
            display: none !important;
        }

        /* Ensure custom navigation buttons are properly positioned */
        .swiper-button-next-custom,
        .swiper-button-prev-custom {
            position: absolute;
            top: 50%;
            z-index: 20;
            cursor: pointer;
            user-select: none;
        }

        /* Featured packages section specific styles */
        #packages .swiper-container {
            overflow: visible;
        }

        /* Hero section animations */
        .hero-content {
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Section animations */
        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }

        /* Why Explore with Us - Gallery-style hover effect */
        .why-us-image-container {
            position: relative;
            overflow: hidden;
        }

        .why-us-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 50%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .why-us-image-container:hover .why-us-overlay {
            opacity: 1;
        }

        .why-us-image-container:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .why-us-image-container:hover img {
            transform: scale(1.05);
        }

        /* Enhanced text shadow for better readability */
        .why-us-overlay .text-shadow {
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative h-[100vh] bg-cover bg-center bg-no-repeat parallax-bg" style="background-image: url('images/hero-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>
        
        <!-- Hero Content -->
        <div class="relative z-10 flex flex-col items-center justify-center text-center h-full px-4">
            <h1 class="text-4xl md:text-6xl font-semibold text-white mb-4 text-shadow">
                Explore <span class="text-orange-400">Africa</span> with Us
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-6"></div>
            <p class="text-xl md:text-2xl text-white mb-8 max-w-3xl text-shadow">Experience authentic and unforgettable safari adventures tailored just for you.</p>
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="#destinations" class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105">Explore Destinations</a>
                <a href="request-quote.php" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-full font-semibold transition duration-300">Plan Your Safari</a>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="3" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
        </div>
    </section>

    <!-- Popular Destinations Section -->
    <section id="destinations" class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Popular <span class="gradient-text">Destinations</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Discover Kenya's most breathtaking destinations, from world-renowned safari parks to pristine coastal paradises
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php foreach ($displayDestinations as $index => $destination): ?>
                    <!-- Destination Card <?php echo $index + 1; ?> -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden destination-card image-hover-zoom">
                        <?php if ($destination['display_image_url']): ?>
                            <img data-src="admin-dashboard/<?php echo htmlspecialchars($destination['display_image_url']); ?>"
                                alt="<?php echo htmlspecialchars($destination['name'] . ' safari destination in Kenya - wildlife and scenic landscapes'); ?>"
                                class="w-full h-48 object-cover lazy-image"
                                loading="lazy"
                                decoding="async">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <div class="text-center text-gray-500">
                                    <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-sm">No Image Available</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="relative p-6 pt-2 h-32">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                <?php echo Utils::displayText($destination['name']); ?>
                            </h3>
                            <p class="text-gray-700 mb-4 text-sm line-clamp-2">
                                <?php
                                $description = $destination['short_description'] ?: 'Discover this amazing destination with unique experiences and unforgettable memories.';
                                echo Utils::displayText($description);
                                ?>
                            </p>
                            <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                <span class="text-base font-semibold gradient-text">
                                    <?php if ($destination['price'] > 0): ?>
                                        From $<?php echo number_format($destination['price']); ?>
                                    <?php else: ?>
                                        From $0
                                    <?php endif; ?>
                                </span>
                                <?php if ($destination['destination_id']): ?>
                                    <a href="destination-details.php?id=<?php echo $destination['destination_id']; ?>&from=index"
                                    class="text-orange-500 hover:text-orange-600 font-medium transition duration-300">
                                        Learn More →
                                    </a>
                                <?php else: ?>
                                    <span class="text-gray-400 font-medium">
                                        Coming Soon
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- View More Destinations Button -->
            <div class="text-center mt-12">
                <a href="tours.php#destinations" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                    View All Destinations
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Tour Packages Section - Now with dark background -->
    <section id="packages" class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold mb-6">
                    Featured <span class="text-orange-400">Tour Packages</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Carefully curated safari experiences designed to create unforgettable memories
                </p>
            </div>
            
            <!-- Navigation and Swiper Container -->
            <div class="relative">
                <!-- Swiper Container with proper spacing for navigation buttons -->
                <div class="swiper mx-0 md:mx-16">
                    <div class="swiper-wrapper">
                        <?php foreach ($displayPackages as $index => $package): ?>
                            <!-- Package Slide <?php echo $index + 1; ?> -->
                            <div class="swiper-slide p-4">
                                <div class="bg-gray-900 rounded-2xl shadow-lg overflow-hidden card-hover image-hover-zoom">
                                    <?php if ($package['display_image_url']): ?>
                                        <img data-src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                            alt="<?php echo htmlspecialchars($package['name'] . ' safari package - ' . ($package['type_name'] ?? 'tour') . ' in Kenya'); ?>"
                                            class="w-full h-60 object-cover lazy-image"
                                            loading="lazy"
                                            decoding="async">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                            <div class="text-center text-white">
                                                <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p class="text-sm">Coming Soon</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="relative pt-4 p-6 h-48">
                                        <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                            <?php
                                            if ($package['tour_package_id']) {
                                                // Smart formatting based on package type and duration
                                                $packageName = $package['name'];
                                                $tourType = $package['type_name'] ?: 'Tour';
                                                $duration = $package['duration'];

                                                // Check if tour type already implies duration
                                                $lowerTourType = strtolower($tourType);
                                                $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                                    strpos($lowerTourType, 'overnight') !== false ||
                                                                    strpos($lowerTourType, 'night') !== false);

                                                if ($typeImpliesDuration) {
                                                    // Format: Tour Type + Package Name
                                                    $formattedName = $tourType . ' ' . $packageName;
                                                } elseif (!empty($duration)) {
                                                    // Format: Duration + Package Name + Tour Type
                                                    $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                                } else {
                                                    // Format: Package Name + Tour Type
                                                    $formattedName = $packageName . ' ' . $tourType;
                                                }

                                                echo Utils::displayText($formattedName);
                                            } else {
                                                echo Utils::displayText($package['name']);
                                            }
                                            ?>
                                        </h3>
                                        <p class="text-gray-200 mb-4 text-base line-clamp-2">
                                            <?php
                                            $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                            echo Utils::displayText($description);
                                            ?>
                                        </p>
                                        <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                            <span class="text-base font-semibold text-orange-400">
                                                $<?php echo number_format($package['price']); ?>
                                            </span>
                                            <?php if ($package['tour_package_id']): ?>
                                                <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>&from=index"
                                                class="text-sm bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full font-medium transition duration-300 flex items-center">
                                                    View Details <i class="fas fa-arrow-right ml-2"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-sm bg-gray-600 text-white px-6 py-2 rounded-full font-medium">
                                                    Coming Soon
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination Bullets -->
                    <div class="swiper-pagination mt-8"></div>
                </div>

                <!-- Navigation Buttons - Positioned outside the swiper container -->
                <button class="swiper-button-prev-custom hidden md:flex absolute left-0 top-1/2 -translate-y-1/2 -translate-x-6 z-20 w-14 h-14 bg-gray-900 hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl items-center justify-center text-orange-500 hover:text-orange-400 transition-all duration-300 group">
                    <svg class="w-6 h-6 transition-transform group-hover:-translate-x-0.5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>

                <button class="swiper-button-next-custom hidden md:flex absolute right-0 top-1/2 -translate-y-1/2 translate-x-6 z-20 w-14 h-14 bg-gray-900 hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl items-center justify-center text-orange-500 hover:text-orange-400 transition-all duration-300 group">
                    <svg class="w-6 h-6 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <!-- View More Packages Button -->
            <div class="text-center mt-12">
                <a href="tours.php#packages" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                    View All Packages
                </a>
            </div>
        </div>
    </section>

    <!-- Why Explore with Us Section - Now with light background and new layout -->
    <section class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <!-- Centered Heading Section -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Why Explore with <span class="gradient-text">Meleva Tours</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Experience the difference of authentic African adventures with local expertise and personalized service
                </p>
            </div>
            
            <!-- Left-Right Content Division -->
            <div class="flex flex-col lg:flex-row gap-12 items-center">
                <!-- Key Points on Left -->
                <div class="lg:w-1/2">
                    <div class="space-y-8">
                        <!-- Key Point 1 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Local Expertise</h3>
                                <p class="text-gray-600 leading-relaxed">Our local experts ensure authentic and immersive experiences, sharing deep knowledge of Africa's culture and wildlife.</p>
                            </div>
                        </div>
                        
                        <!-- Key Point 2 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Tailored Itineraries</h3>
                                <p class="text-gray-600 leading-relaxed">Customized travel plans designed to match your interests, from adventure to relaxation, for a perfect journey that exceeds expectations.</p>
                            </div>
                        </div>
                        
                        <!-- Key Point 3 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Sustainable Travel</h3>
                                <p class="text-gray-600 leading-relaxed">We prioritize eco-friendly practices and support local communities, ensuring your trip makes a positive impact on conservation efforts.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Image on Right -->
                <div class="lg:w-1/2">
                    <div class="relative rounded-2xl overflow-hidden shadow-xl h-96 lg:h-[500px] transform transition-all duration-500 hover:shadow-2xl hover:scale-105 cursor-pointer why-us-image-container">
                        <img src="images/why-us.jpg" alt="Meleva Tours Safari Experience"
                            class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">

                        <!-- Gallery-style overlay that appears on hover -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 transition-opacity duration-300 why-us-overlay">
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-3 text-shadow">Authentic African Experiences</h3>
                                <p class="text-base leading-relaxed text-shadow">
                                    Discover the untamed beauty of Africa through our expertly guided safaris. From the majestic wildlife of Kenya's national parks to the pristine beaches of the coast, every moment is crafted to create lasting memories.
                                </p>
                                <div class="mt-4 flex items-center text-orange-300">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    <span class="text-sm font-medium">Experience the Adventure</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust & Accreditations Section -->
    <section class="py-10 px-4 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Our <span class="gradient-text">Accreditations</span> & Booking Security
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-4xl mx-auto">
                    Meleva Tours and Travel is fully licensed and regulated, ensuring your safari experience is safe, secure, and professionally managed
                </p>
            </div>

            <!-- Security Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <!-- Secure Booking -->
                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Secure Payments</h4>
                    <p class="text-sm text-gray-600">SSL encrypted booking system with secure payment processing</p>
                </div>

                <!-- Professional Guides -->
                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Certified Guides</h4>
                    <p class="text-sm text-gray-600">Licensed professional guides with extensive local knowledge</p>
                </div>

                <!-- Safety Standards -->
                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Safety Certified</h4>
                    <p class="text-sm text-gray-600">ISO 9001 standards with comprehensive insurance coverage</p>
                </div>

                <!-- Environmental -->
                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-teal-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Eco-Certified</h4>
                    <p class="text-sm text-gray-600">Sustainable tourism practices supporting local communities</p>
                </div>
            </div>

            <!-- Partner Logos Section -->
            <div class="mt-16">
                <h3 class="text-2xl font-semibold text-gray-900 mb-12 text-center">Our Trusted Partners</h3>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center">
                    <!-- Kenya Tourism Board -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/Kenya-Tourism-Board.png"
                             alt="Kenya Tourism Board"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- Magical Kenya -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/Magical-Kenya.png"
                             alt="Magical Kenya"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- Pesapal Payment Partner -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/Pesapal.png"
                             alt="Pesapal Payment Gateway"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- SafariBookings -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/SafariBookings.png"
                             alt="SafariBookings"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- TripAdvisor -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/Tripadvisor.png"
                             alt="TripAdvisor"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- AMREF -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/Amref.png"
                             alt="AMREF Health Africa"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                    <!-- TOSK -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/tosk.webp"
                             alt="TOSK"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>
                    
                    <!-- Kenya Wildlife Services -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300 flex items-center justify-center w-full h-24 group">
                        <img src="images/logos/kws.png"
                             alt="Kenya Wildlife Services"
                             class="max-h-16 max-w-full w-auto h-auto object-contain group-hover:grayscale transition-all duration-300"
                             loading="lazy">
                    </div>

                </div>

                <!-- Partner Description -->
                <div class="mt-8 text-center">
                    <p class="text-gray-600 max-w-3xl mx-auto">
                        We work with trusted partners and are members of leading tourism organizations to ensure the highest standards of service, safety, and sustainability in all our safari experiences.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <?php
    // Fetch Google Business Profile Reviews
    require_once 'includes/google-reviews.php';
    $google_reviews = new GoogleReviews();
    $reviews = $google_reviews->getReviews(3);
    ?>

    <!-- Testimonials Section -->
    <section class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    What Our <span class="gradient-text">Travelers Say</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Real experiences from adventurers who have explored Africa with us
                </p>

                <!-- Google Reviews Badge -->
                <div class="flex items-center justify-center mt-6 space-x-4">
                    <div class="flex items-center bg-white rounded-lg shadow-md px-4 py-2">
                        <img src="images/logos/google.png" alt="Google" class="w-6 h-6 mr-2">
                        <span class="text-sm font-medium text-gray-700">Reviews from Google</span>
                    </div>
                    <a href="https://www.google.com/search?q=Meleva+Tours+and+Travel" target="_blank" class="text-orange-500 hover:text-orange-600 text-sm font-medium transition duration-300">
                        View All Reviews →
                    </a>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php foreach ($reviews as $index => $review): ?>
                <!-- Review <?php echo $index + 1; ?> -->
                <div class="bg-white rounded-2xl shadow-lg p-8 card-hover border-l-4 safari-border">
                    <div class="flex items-center justify-between mb-4">
                        <?php echo $google_reviews->generateStarRating($review['rating']); ?>
                        <div class="flex items-center text-xs text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            Google
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">"<?php echo htmlspecialchars($review['text']); ?>"</p>
                    <div class="flex items-center">
                        <?php echo $google_reviews->generateAvatar($review); ?>
                        <div>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($review['name']); ?></p>
                            <p class="text-sm text-gray-500"><?php echo htmlspecialchars($review['date']); ?></p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- View More Reviews Button -->
            <div class="text-center mt-12">
                <a href="https://www.google.com/search?q=Meleva+Tours+and+Travel" target="_blank" class="inline-flex items-center bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-3 rounded-full font-semibold hover:from-orange-600 hover:to-red-600 transition duration-300 transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    View All Google Reviews
                </a>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 px-4 bg-gray-50">
        <div class="max-w-4xl mx-auto">
            <?php
            require_once 'includes/faq.php';
            $faq = new FAQ();
            echo $faq->generateFAQSection('Frequently Asked Questions', 6);
            ?>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="relative py-20 px-4 text-white overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('images/cta-banner.jpg'); filter: blur(2px);">
            <!-- Dark Overlay for better text readability -->
            <div class="absolute inset-0 bg-black bg-opacity-80"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6 text-white drop-shadow-lg">
                Ready to Discover Africa?
            </h2>
            <p class="text-lg md:text-xl mb-10 text-white drop-shadow-md max-w-3xl mx-auto leading-relaxed">
                Let us craft your perfect African adventure. From the coastal beauty of Kilifi to the wild heart of Kenya's national parks, your journey of a lifetime awaits.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="request-quote.php" class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    Plan Your Safari
                </a>
                <a href="about.php" class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl">
                    Learn About Us
                </a>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-white opacity-20 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 border-2 border-orange-400 opacity-30 rounded-full"></div>
        <div class="absolute top-1/2 left-5 w-3 h-3 bg-orange-400 opacity-40 rounded-full"></div>
        <div class="absolute top-1/4 right-20 w-2 h-2 bg-white opacity-50 rounded-full"></div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        // ===================================
        // INDEX PAGE SPECIFIC JAVASCRIPT
        // ===================================

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Swiper for tour packages
            const swiper = new Swiper('.swiper', {
                loop: true,
                slidesPerView: 1,
                spaceBetween: 20,
                navigation: {
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: true,
                    dynamicMainBullets: 3,
                },
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                    },
                    1024: {
                        slidesPerView: 3,
                    },
                },
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all sections for fade-in animation
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });
        });
    </script>

    <!-- Image Optimization -->
    <script src="js/image-optimizer.js"></script>

    <!-- Structured Data for Homepage -->
    <script type="application/ld+json">
    <?php
    require_once 'includes/seo-meta.php';
    $seoMeta = new SEOMeta();
    echo $seoMeta->generateStructuredData(['type' => 'Organization']);
    ?>
    </script>

    <!-- FAQ Structured Data -->
    <?php
    require_once 'includes/faq.php';
    $faq = new FAQ();
    echo $faq->generateStructuredData();
    echo $faq->generateFAQScript();
    ?>

    <?php include 'footer.php'; ?>

</body>
</html>

