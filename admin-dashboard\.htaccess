# Set directory index to login.php
DirectoryIndex login.php index.php

# Redirect root directory access to login page
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} -d
RewriteCond %{REQUEST_URI} /admin-dashboard/?$
RewriteRule ^(.*)$ login.php [R=302,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;"
</IfModule>

# Prevent access to sensitive files
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.*">
    Order allow,deny
    Deny from all
</Files>

# Protect log files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Protect backup files
<Files "*~">
    Order allow,deny
    Deny from all
</Files>

# Protect configuration files
<Files "*.conf">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to includes directory
<Files "includes/*">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to logs directory
<Files "logs/*">
    Order allow,deny
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes

# Prevent access to PHP files in upload directory
<Files "upload/*.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to backup files
<Files "*~">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

# Prevent directory browsing
Options -Indexes

# Disable error reporting for production
php_flag display_errors Off
php_flag display_startup_errors Off
