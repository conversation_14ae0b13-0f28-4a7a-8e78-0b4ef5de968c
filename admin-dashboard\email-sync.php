<?php
/**
 * Email Synchronization Page
 * Admin interface for managing email fetching
 */

session_start();
require_once 'config/config.php';
require_once 'classes/Auth.php';

// Check authentication
if (!Auth::isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle manual email fetch
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fetch_emails'])) {
    try {
        require_once 'classes/ImapEmailFetcher.php';
        
        $fetcher = new ImapEmailFetcher();
        $results = $fetcher->fetchAllEmails();
        
        $totalProcessed = 0;
        $details = [];
        
        foreach ($results as $email => $result) {
            if (isset($result['error'])) {
                $details[] = "$email: ERROR - " . $result['error'];
            } else {
                $totalProcessed += $result['processed'];
                $details[] = "$email: {$result['processed']} new emails processed";
            }
        }
        
        $message = "Email sync completed! Total: $totalProcessed new emails processed.";
        $messageType = 'success';
        
        if (!empty($details)) {
            $message .= "<br><small>" . implode('<br>', $details) . "</small>";
        }
        
    } catch (Exception $e) {
        $message = 'Email sync failed: ' . $e->getMessage();
        $messageType = 'error';
        error_log("Manual email sync error: " . $e->getMessage());
    }
}

// Get last sync time
$lastSyncFile = 'cache/last_email_fetch.txt';
$lastSync = file_exists($lastSyncFile) ? file_get_contents($lastSyncFile) : 'Never';

// Get recent email statistics
try {
    $db = Database::getInstance()->getConnection();
    
    // Count emails from last 24 hours
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND message_type = 'incoming'");
    $stmt->execute();
    $recentEmails = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count unread messages
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE is_read = 0 AND message_type = 'incoming'");
    $stmt->execute();
    $unreadCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Count total conversations
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM email_conversations");
    $stmt->execute();
    $totalConversations = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
} catch (Exception $e) {
    $recentEmails = 'Error';
    $unreadCount = 'Error';
    $totalConversations = 'Error';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Synchronization - Meleva Admin</title>
    <link rel="stylesheet" href="../dist/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold text-gray-900">Email Synchronization</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="messages.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-envelope mr-1"></i> Messages
                        </a>
                        <a href="dashboard.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-dashboard mr-1"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Status Message -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-50 border border-green-200 text-green-800' : 'bg-red-50 border border-red-200 text-red-800'; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- Email Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i class="fas fa-envelope text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Last 24 Hours</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo $recentEmails; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-100 rounded-lg">
                            <i class="fas fa-envelope-open text-orange-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Unread Messages</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo $unreadCount; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-comments text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Conversations</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo $totalConversations; ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <i class="fas fa-clock text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Last Sync</p>
                            <p class="text-sm font-semibold text-gray-900"><?php echo $lastSync; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Manual Sync Section -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Manual Email Synchronization</h2>
                <p class="text-gray-600 mb-4">
                    Click the button below to manually fetch new emails from the IMAP server. 
                    This will <NAME_EMAIL> and <EMAIL> accounts.
                </p>
                
                <form method="POST" class="inline">
                    <button type="submit" name="fetch_emails" 
                            class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Fetch New Emails
                    </button>
                </form>
            </div>

            <!-- Configuration Info -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Email Configuration</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">IMAP Settings</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><strong>Server:</strong> melevatours.co.ke</li>
                            <li><strong>Port:</strong> 993 (SSL)</li>
                            <li><strong>Encryption:</strong> SSL/TLS</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Monitored Accounts</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li><i class="fas fa-envelope text-blue-500 mr-1"></i> <EMAIL> (Contact)</li>
                            <li><i class="fas fa-envelope text-green-500 mr-1"></i> <EMAIL> (Quotes)</li>
                        </ul>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="font-medium text-blue-900 mb-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        Automatic Synchronization
                    </h4>
                    <p class="text-sm text-blue-800">
                        For automatic email fetching, set up a cron job to run every 5 minutes:<br>
                        <code class="bg-blue-100 px-2 py-1 rounded text-xs">
                            */5 * * * * /usr/bin/php /path/to/admin-dashboard/cron/email-fetch-cron.php
                        </code>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh page every 30 seconds if there are unread messages
        <?php if ($unreadCount > 0): ?>
        setTimeout(function() {
            location.reload();
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
