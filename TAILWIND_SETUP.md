# Tailwind CSS Setup for Meleva Tours

## Overview
This project now uses a proper Tailwind CSS setup instead of the CDN version, which eliminates the production warning and provides better performance and customization options.

## File Structure
```
/
├── package.json           # Node.js dependencies and scripts
├── tailwind.config.js     # Tailwind configuration
├── src/
│   └── input.css         # Source CSS with Tailwind directives
├── dist/
│   └── tailwind.css      # Compiled CSS (generated)
├── build.bat             # Windows build script
└── TAILWIND_SETUP.md     # This documentation
```

## Setup Complete ✅
The following has been implemented:

1. **Tailwind CLI Installation**: Installed via npm for local development
2. **Configuration**: Custom `tailwind.config.js` with Meleva brand colors and settings
3. **Source CSS**: `src/input.css` with Tailwind directives and custom styles
4. **Build Process**: Automated compilation from source to production CSS
5. **File Updates**: All PHP files updated to use compiled CSS instead of CDN

## Available Scripts

### Development (with file watching)
```bash
npm run dev
```
This watches for changes and rebuilds automatically.

### Production Build
```bash
npm run build
```
This creates a minified production build.

### Windows Users
Double-click `build.bat` for a simple build process.

## Custom Features

### Brand Colors
- **Meleva Orange**: `meleva-orange-500` (#f97316)
- **Meleva Red**: `meleva-red-500` (#ef4444)

### Custom Components
- `.btn-primary` - Primary button styling
- `.btn-secondary` - Secondary button styling
- `.destination-card` - Destination card hover effects
- `.package-card` - Package card styling
- `.form-input` - Form input styling
- `.hero-section` - Hero section utilities

### Custom Utilities
- `.text-shadow` - Text shadow effects
- `.glass-effect` - Glass morphism effect
- `.gradient-bg` - Custom gradient backgrounds

## Development Workflow

1. **Making Style Changes**: Edit `src/input.css`
2. **Building**: Run `npm run build` or `npm run dev`
3. **Testing**: The compiled CSS at `dist/tailwind.css` is automatically used by all pages

## Performance Benefits

- ✅ **No CDN dependency**: Faster loading, works offline
- ✅ **Purged CSS**: Only includes used classes (smaller file size)
- ✅ **Custom optimizations**: Tailored for your specific needs
- ✅ **No production warnings**: Clean console output

## Maintenance

### Adding New Styles
1. Add custom CSS to `src/input.css`
2. Run build process
3. Test changes

### Updating Tailwind
```bash
npm update tailwindcss
```

### Content Configuration
The `tailwind.config.js` is configured to scan:
- All PHP files in root directory
- All PHP files in admin-dashboard
- All JavaScript files in js/ directory

## Troubleshooting

### Build Issues
- Ensure Node.js is installed
- Run `npm install` if dependencies are missing
- Check that `src/input.css` exists

### Missing Styles
- Verify the class is used in scanned files
- Check `tailwind.config.js` content paths
- Rebuild with `npm run build`

### File Not Found Errors
- Ensure `dist/tailwind.css` exists
- Run build process if missing
- Check file paths in PHP includes

## Migration Complete ✅

All files have been successfully updated:
- ❌ `<script src="https://cdn.tailwindcss.com"></script>`
- ✅ `<link rel="stylesheet" href="dist/tailwind.css">`

The website now uses a production-ready Tailwind CSS setup without any CDN dependencies or warnings.
