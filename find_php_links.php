<?php
/**
 * Find remaining .php links in files
 */

echo "=== FINDING REMAINING .PHP LINKS ===\n\n";

$directories = ['.'];
$phpFiles = [];
$excludePatterns = [
    '/admin-dashboard/',
    '/vendor/',
    '/node_modules/',
    '/.git/',
    '/upload/',
    '/images/',
    '/js/',
    '/css/',
    'find_php_links.php'
];

// Get all PHP files
function scanDirectory($dir, &$files, $excludePatterns) {
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $path = $dir . '/' . $item;
        
        // Check if path should be excluded
        $shouldExclude = false;
        foreach ($excludePatterns as $pattern) {
            if (strpos($path, $pattern) !== false) {
                $shouldExclude = true;
                break;
            }
        }
        
        if ($shouldExclude) continue;
        
        if (is_dir($path)) {
            scanDirectory($path, $files, $excludePatterns);
        } elseif (pathinfo($path, PATHINFO_EXTENSION) === 'php') {
            $files[] = $path;
        }
    }
}

scanDirectory('.', $phpFiles, $excludePatterns);

echo "Scanning " . count($phpFiles) . " PHP files...\n\n";

$foundLinks = [];

foreach ($phpFiles as $file) {
    $content = file_get_contents($file);
    
    // Look for href="*.php" patterns
    if (preg_match_all('/href=["\']([^"\']*\.php[^"\']*)["\']/', $content, $matches)) {
        foreach ($matches[1] as $link) {
            // Skip external links and admin links
            if (strpos($link, 'http') === 0 || strpos($link, 'admin-dashboard') !== false) {
                continue;
            }
            
            $foundLinks[] = [
                'file' => $file,
                'link' => $link,
                'line' => 'N/A' // We could get line numbers but it's complex
            ];
        }
    }
    
    // Look for action="*.php" patterns
    if (preg_match_all('/action=["\']([^"\']*\.php[^"\']*)["\']/', $content, $matches)) {
        foreach ($matches[1] as $link) {
            if (strpos($link, 'http') === 0 || strpos($link, 'admin-dashboard') !== false) {
                continue;
            }
            
            $foundLinks[] = [
                'file' => $file,
                'link' => $link,
                'type' => 'form action'
            ];
        }
    }
    
    // Look for Location: redirects
    if (preg_match_all('/Location:\s*([^"\'\s]*\.php[^"\'\s]*)/', $content, $matches)) {
        foreach ($matches[1] as $link) {
            $foundLinks[] = [
                'file' => $file,
                'link' => $link,
                'type' => 'redirect'
            ];
        }
    }
}

if (empty($foundLinks)) {
    echo "✅ No .php links found in your files!\n";
} else {
    echo "❌ Found " . count($foundLinks) . " .php links that need updating:\n\n";
    
    $groupedByFile = [];
    foreach ($foundLinks as $link) {
        $groupedByFile[$link['file']][] = $link;
    }
    
    foreach ($groupedByFile as $file => $links) {
        echo "📄 $file:\n";
        foreach ($links as $link) {
            $type = isset($link['type']) ? " ({$link['type']})" : '';
            echo "   - {$link['link']}$type\n";
        }
        echo "\n";
    }
}

// Also check for common pages that might need updating
$commonPages = [
    'index.php', 'about.php', 'contact.php', 'tours.php', 
    'gallery.php', 'request-quote.php', 'destination-details.php', 
    'package-details.php', 'privacy-policy.php', 'terms-of-service.php',
    'cookie-policy.php'
];

echo "=== CHECKING COMMON PAGE REFERENCES ===\n";
$pageReferences = [];

foreach ($phpFiles as $file) {
    $content = file_get_contents($file);
    
    foreach ($commonPages as $page) {
        if (strpos($content, $page) !== false) {
            // Count occurrences
            $count = substr_count($content, $page);
            if (!isset($pageReferences[$page])) {
                $pageReferences[$page] = [];
            }
            $pageReferences[$page][] = ['file' => $file, 'count' => $count];
        }
    }
}

foreach ($pageReferences as $page => $references) {
    $totalCount = array_sum(array_column($references, 'count'));
    echo "$page: $totalCount references across " . count($references) . " files\n";
}

echo "\n🎯 RECOMMENDATION:\n";
echo "1. Update any remaining .php links found above\n";
echo "2. Test your website to ensure all links work\n";
echo "3. Check for any JavaScript redirects or AJAX calls\n";
echo "4. Update any canonical URLs or meta tags\n";
echo "5. Update your sitemap if you have one\n";

?>
