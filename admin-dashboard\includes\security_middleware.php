<?php
/**
 * Security Middleware for Admin Pages
 * Include this file at the top of every admin page for comprehensive security
 */

// Prevent direct access
if (!defined('ADMIN_ACCESS')) {
    die('Direct access not allowed');
}

// Start output buffering to prevent header issues
if (!ob_get_level()) {
    ob_start();
}

// Include required files
require_once __DIR__ . '/../config/config.php';

// Start secure session
Auth::startSession();

// Security Headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src \'self\' data: blob: https:; font-src \'self\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;');

// Require authentication
Auth::requireLogin();

// Additional security checks for POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || !Utils::validateCSRFToken($_POST['csrf_token'])) {
        http_response_code(403);
        die('CSRF token validation failed');
    }
    
    // Rate limiting for POST requests
    if (!Utils::checkRateLimit('admin_post', 30, 300)) {
        http_response_code(429);
        die('Rate limit exceeded. Please wait before trying again.');
    }
}

// Additional security checks for file uploads
if (!empty($_FILES)) {
    foreach ($_FILES as $file) {
        if ($file['error'] === UPLOAD_ERR_OK) {
            $validation = Utils::validateFileUpload($file);
            if (!$validation['valid']) {
                http_response_code(400);
                die('File upload security check failed: ' . $validation['error']);
            }
        }
    }
}

// Log page access for security monitoring
Auth::logSecurityEvent('page_access', Auth::getCurrentUser()['username'] ?? 'unknown');

// Function to generate CSRF token for forms
function csrf_token() {
    return Utils::generateCSRFToken();
}

// Function to create CSRF hidden input
function csrf_field() {
    return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
}

// Function to check if user has specific role
function hasRole($role) {
    $user = Auth::getCurrentUser();
    return $user && $user['role'] === $role;
}

// Function to require specific role
function requireRole($role) {
    if (!hasRole($role)) {
        http_response_code(403);
        die('Insufficient permissions');
    }
}

// Auto-logout warning JavaScript
function injectAutoLogoutScript() {
    $timeoutSeconds = DatabaseConfig::SESSION_TIMEOUT;
    $warningTime = $timeoutSeconds - 300; // 5 minutes before timeout
    
    echo "
    <script>
    (function() {
        let warningShown = false;
        let logoutTimer;
        let warningTimer;
        
        function resetTimers() {
            clearTimeout(logoutTimer);
            clearTimeout(warningTimer);
            warningShown = false;
            
            // Set warning timer
            warningTimer = setTimeout(function() {
                if (!warningShown) {
                    warningShown = true;
                    if (confirm('Your session will expire in 5 minutes due to inactivity. Click OK to stay logged in.')) {
                        // Refresh session by making a simple request
                        fetch('api/refresh_session.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({csrf_token: '" . csrf_token() . "'})
                        }).then(() => {
                            resetTimers();
                        });
                    }
                }
            }, " . ($warningTime * 1000) . ");
            
            // Set logout timer
            logoutTimer = setTimeout(function() {
                alert('Session expired due to inactivity. You will be redirected to login page.');
                window.location.href = 'login.php';
            }, " . ($timeoutSeconds * 1000) . ");
        }
        
        // Reset timers on user activity
        document.addEventListener('click', resetTimers);
        document.addEventListener('keypress', resetTimers);
        document.addEventListener('scroll', resetTimers);
        document.addEventListener('mousemove', resetTimers);
        
        // Initialize timers
        resetTimers();
    })();
    </script>";
}

// Function to display security status
function getSecurityStatus() {
    $user = Auth::getCurrentUser();
    return [
        'user' => $user,
        'session_time_remaining' => DatabaseConfig::SESSION_TIMEOUT - (time() - ($_SESSION['last_activity'] ?? time())),
        'login_time' => $_SESSION['login_time'] ?? null,
        'user_ip' => Auth::getUserIP(),
        'csrf_token' => csrf_token()
    ];
}
