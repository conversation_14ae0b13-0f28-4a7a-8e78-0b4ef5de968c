<?php
/**
 * Internal Linking Helper for Meleva Tours and Travel
 * Provides strategic internal linking for better SEO and user experience
 */

class InternalLinks {
    private $baseUrl;
    
    public function __construct() {
        $this->baseUrl = $this->getBaseUrl();
    }
    
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            return $protocol . '://' . $host . '/meleva';
        } else {
            return $protocol . '://' . $host;
        }
    }
    
    /**
     * Generate related destination links
     */
    public function getRelatedDestinations($currentDestinationId = null, $limit = 3) {
        try {
            require_once 'admin-dashboard/classes/models.php';
            $destinationModel = new Destination();
            $destinations = $destinationModel->findAllWithImages();
            
            // Filter out current destination
            if ($currentDestinationId) {
                $destinations = array_filter($destinations, function($dest) use ($currentDestinationId) {
                    return $dest['destination_id'] != $currentDestinationId;
                });
            }
            
            // Shuffle and limit
            shuffle($destinations);
            return array_slice($destinations, 0, $limit);
            
        } catch (Exception $e) {
            error_log("Error fetching related destinations: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Generate related package links
     */
    public function getRelatedPackages($currentPackageId = null, $packageTypeId = null, $limit = 3) {
        try {
            require_once 'admin-dashboard/classes/models.php';
            $packageModel = new TourPackage();
            $packages = $packageModel->findAllWithDetails();
            
            // Filter out current package
            if ($currentPackageId) {
                $packages = array_filter($packages, function($pkg) use ($currentPackageId) {
                    return $pkg['tour_package_id'] != $currentPackageId;
                });
            }
            
            // Prioritize same package type
            if ($packageTypeId) {
                $sameTypePackages = array_filter($packages, function($pkg) use ($packageTypeId) {
                    return $pkg['package_type_id'] == $packageTypeId;
                });
                
                if (count($sameTypePackages) >= $limit) {
                    $packages = $sameTypePackages;
                }
            }
            
            // Shuffle and limit
            shuffle($packages);
            return array_slice($packages, 0, $limit);
            
        } catch (Exception $e) {
            error_log("Error fetching related packages: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Generate contextual links section
     */
    public function generateRelatedLinksSection($type = 'destinations', $currentId = null, $additionalData = null) {
        if ($type === 'destinations') {
            $items = $this->getRelatedDestinations($currentId);
            $title = 'Explore More Destinations';
            $linkPrefix = 'destination-details?id=';
        } else {
            $packageTypeId = $additionalData['package_type_id'] ?? null;
            $items = $this->getRelatedPackages($currentId, $packageTypeId);
            $title = 'Similar Safari Packages';
            $linkPrefix = 'package-details?id=';
        }
        
        if (empty($items)) {
            return '';
        }
        
        $html = '<section class="mt-16 bg-gray-50 rounded-2xl p-8">';
        $html .= '<div class="text-center mb-8">';
        $html .= '<h2 class="text-2xl md:text-3xl font-semibold text-gray-900 mb-4">' . $title . '</h2>';
        $html .= '<div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto"></div>';
        $html .= '</div>';
        
        $html .= '<div class="grid grid-cols-1 md:grid-cols-' . min(count($items), 3) . ' gap-6">';
        
        foreach ($items as $item) {
            $html .= $this->generateItemCard($item, $type, $linkPrefix);
        }
        
        $html .= '</div>';
        
        // Add call-to-action
        if ($type === 'destinations') {
            $html .= '<div class="text-center mt-8">';
            $html .= '<a href="' . $this->baseUrl . '/tours.php#destinations" class="inline-flex items-center text-orange-500 hover:text-orange-600 font-medium">';
            $html .= 'View All Destinations <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path></svg>';
            $html .= '</a>';
            $html .= '</div>';
        } else {
            $html .= '<div class="text-center mt-8">';
            $html .= '<a href="' . $this->baseUrl . '/tours.php#packages" class="inline-flex items-center text-orange-500 hover:text-orange-600 font-medium">';
            $html .= 'View All Packages <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path></svg>';
            $html .= '</a>';
            $html .= '</div>';
        }
        
        $html .= '</section>';
        
        return $html;
    }
    
    private function generateItemCard($item, $type, $linkPrefix) {
        if ($type === 'destinations') {
            $id = $item['destination_id'];
            $name = $item['name'];
            $description = $item['short_description'] ?? '';
            $image = $item['display_image_url'] ?? '';
            $price = $item['price'] ?? 0;
        } else {
            $id = $item['tour_package_id'];
            $name = $item['name'];
            $description = $item['description'] ?? '';
            $image = $item['display_image_url'] ?? '';
            $price = $item['price'] ?? 0;
        }
        
        $html = '<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition duration-300">';
        
        // Image
        if ($image) {
            $html .= '<div class="h-48 overflow-hidden">';
            $html .= '<img src="admin-dashboard/' . htmlspecialchars($image) . '" ';
            $html .= 'alt="' . htmlspecialchars($name) . '" ';
            $html .= 'class="w-full h-full object-cover hover:scale-105 transition duration-300">';
            $html .= '</div>';
        } else {
            $html .= '<div class="h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">';
            $html .= '<div class="text-center text-white">';
            $html .= '<i class="fas fa-mountain text-4xl mb-2"></i>';
            $html .= '<p class="text-sm">No Image Available</p>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        // Content
        $html .= '<div class="p-6">';
        $html .= '<h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">' . htmlspecialchars($name) . '</h3>';
        
        if ($description) {
            $html .= '<p class="text-gray-600 text-sm mb-4 line-clamp-2">' . htmlspecialchars(substr($description, 0, 120)) . '...</p>';
        }
        
        // Price and CTA
        $html .= '<div class="flex items-center justify-between">';
        if ($price > 0) {
            $html .= '<span class="text-orange-500 font-bold">From $' . number_format($price) . '</span>';
        } else {
            $html .= '<span class="text-gray-500 text-sm">Price on request</span>';
        }
        
        $html .= '<a href="' . $linkPrefix . $id . '" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-full text-sm font-medium transition duration-300">';
        $html .= 'View Details';
        $html .= '</a>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate quick navigation links
     */
    public function generateQuickNavigation() {
        $links = [
            ['title' => 'All Tours & Destinations', 'url' => '/tours', 'icon' => 'fas fa-map-marked-alt'],
            ['title' => 'Request Custom Quote', 'url' => '/request-quote', 'icon' => 'fas fa-paper-plane'],
            ['title' => 'View Gallery', 'url' => '/gallery', 'icon' => 'fas fa-images'],
            ['title' => 'Contact Us', 'url' => '/contact', 'icon' => 'fas fa-phone']
        ];
        
        $html = '<div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">';
        
        foreach ($links as $link) {
            $html .= '<a href="' . $this->baseUrl . $link['url'] . '" class="flex flex-col items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition duration-300 text-center">';
            $html .= '<i class="' . $link['icon'] . ' text-2xl text-orange-500 mb-2"></i>';
            $html .= '<span class="text-sm font-medium text-gray-700">' . $link['title'] . '</span>';
            $html .= '</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
