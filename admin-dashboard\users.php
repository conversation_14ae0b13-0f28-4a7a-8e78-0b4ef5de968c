<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';

// Require authentication
Auth::requireLogin();

$userModel = new User();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $data = [
                    'username' => Utils::sanitizeInput($_POST['username']),
                    'email' => Utils::sanitizeInput($_POST['email']),
                    'password' => $_POST['password'],
                    'role' => Utils::sanitizeInput($_POST['role'])
                ];
                
                if ($userModel->create($data)) {
                    $success = 'User created successfully!';
                } else {
                    $error = 'Failed to create user.';
                }
                break;
                
            case 'update':
                $id = intval($_POST['id']);
                $data = [
                    'username' => Utils::sanitizeInput($_POST['username']),
                    'email' => Utils::sanitizeInput($_POST['email']),
                    'role' => Utils::sanitizeInput($_POST['role'])
                ];
                
                if (!empty($_POST['password'])) {
                    $data['password'] = $_POST['password'];
                }
                
                if ($userModel->update($id, $data)) {
                    $success = 'User updated successfully!';
                } else {
                    $error = 'Failed to update user.';
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id']);

                // Get user to check role and ID
                $userToDelete = $userModel->findById($id);

                if ($id === $currentUser['user_id']) {
                    $error = 'You cannot delete your own account.';
                } elseif ($userToDelete && $userToDelete['role'] === 'super_admin') {
                    $error = 'Super admin users cannot be deleted.';
                } elseif ($id === 1) { // Prevent deletion of first admin (usually ID 1)
                    $error = 'The primary admin user cannot be deleted.';
                } else {
                    if ($userModel->delete($id)) {
                        $success = 'User deleted successfully!';
                    } else {
                        $error = 'Failed to delete user.';
                    }
                }
                break;
        }
    }
}

// Get all users
$users = $userModel->findAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - Meleva Admin</title>
    <link rel="stylesheet" href="../dist/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Reset body margin and padding - Override global styles */
        body.admin-page {
            margin: 0 !important;
            padding: 0 !important;
            padding-top: 0 !important;
        }

        .sidebar-transition { transition: all 0.3s ease; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); }
        .gradient-bg { background: linear-gradient(135deg, #f97316, #ea580c); }
        .sidebar-active { background-color: rgba(249, 115, 22, 0.1); border-right: 3px solid #f97316; }

        .form-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
            margin: -1px -1px 0 -1px;
            transition: all 0.3s ease;
        }

        .form-header:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
        }

        .form-section {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: white;
        }

        .collapsible-content.expanded {
            max-height: 1000px;
            transition: max-height 0.5s ease-in;
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 admin-page">
<!-- Sidebar Overlay for Mobile -->
<div id="sidebar-overlay" class="sidebar-overlay"></div>

<!-- Sidebar -->
<?php include 'sidebar.php'; ?>

<!-- Main Content -->
<div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Users</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Add User Form (Inline) -->
            <div class="form-section mb-6">
                <div class="form-header cursor-pointer" id="toggleAddFormHeader">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-plus mr-2"></i>Add New User
                        </h3>
                        <div class="text-white hover:text-orange-200 transition-colors">
                            <i class="fas fa-chevron-down transform transition-transform" id="toggleIcon"></i>
                        </div>
                    </div>
                </div>

                <div id="addFormContent" class="collapsible-content">
                    <div class="p-6">
                        <form id="addUserForm" method="POST">
                            <input type="hidden" name="action" value="create">

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                    <input type="text" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                    <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                                    <select name="role" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                        <option value="admin">Admin</option>
                                        <option value="super_admin">Super Admin</option>
                                    </select>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button type="button" onclick="resetAddForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                                <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-plus mr-2"></i>Create User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($users as $user): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['username']); ?></div>
                                                <?php if ($user['user_id'] == $currentUser['user_id']): ?>
                                                    <div class="text-xs text-orange-600">(You)</div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo htmlspecialchars($user['email']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $user['role'] === 'super_admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $user['role'])); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo Utils::formatDate($user['created_at']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="editUser(<?php echo $user['user_id']; ?>)" class="text-orange-600 hover:text-orange-900 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if ($user['user_id'] !== $currentUser['user_id'] && $user['role'] !== 'super_admin' && $user['user_id'] !== 1): ?>
                                            <button onclick="deleteUser(<?php echo $user['user_id']; ?>)" class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add/Edit Modal -->
    <div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add User</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="userForm" method="POST">
                        <input type="hidden" name="action" id="formAction" value="create">
                        <input type="hidden" name="id" id="userId">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                <input type="text" name="username" id="username" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" name="email" id="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                                <select name="role" id="role" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                                    <option value="admin">Admin</option>
                                    <option value="super_admin">Super Admin</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Password <span id="passwordOptional" class="text-gray-500 text-xs hidden">(leave blank to keep current)</span>
                                </label>
                                <input type="password" name="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" onclick="closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 text-white gradient-bg rounded-lg hover:opacity-90">
                                <span id="submitText">Create User</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile sidebar toggle functionality
        document.addEventListener("DOMContentLoaded", function() {
            const sidebarToggle = document.getElementById("sidebar-toggle");
            const sidebar = document.getElementById("sidebar");
            const sidebarOverlay = document.getElementById("sidebar-overlay");
            
            // Toggle sidebar on mobile
            function toggleSidebar() {
                sidebar.classList.toggle("sidebar-open");
                sidebarOverlay.classList.toggle("active");
                document.body.classList.toggle("overflow-hidden");
            }
            
            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove("sidebar-open");
                sidebarOverlay.classList.remove("active");
                document.body.classList.remove("overflow-hidden");
            }
            
            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener("click", toggleSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener("click", closeSidebar);
            }
            
            // Close sidebar when clicking on navigation links on mobile
            const navLinks = sidebar.querySelectorAll("nav a");
            navLinks.forEach(link => {
                link.addEventListener("click", function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener("resize", function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            // Initialize form toggle functionality
            initializeFormToggle();
        });

        // Form toggle functionality
        function initializeFormToggle() {
            const toggleHeader = document.getElementById('toggleAddFormHeader');
            const formContent = document.getElementById('addFormContent');
            const toggleIcon = document.getElementById('toggleIcon');

            if (toggleHeader && formContent && toggleIcon) {
                toggleHeader.addEventListener('click', function() {
                    formContent.classList.toggle('expanded');
                    toggleIcon.classList.toggle('rotate-180');
                });
            }
        }

        // Reset add form
        function resetAddForm() {
            document.getElementById('addUserForm').reset();
        }

        // User management JavaScript (decode HTML entities for proper display)
        const users = <?php
            // Decode HTML entities in the data before JSON encoding
            $decodedUsers = array_map(function($user) {
                if (isset($user['username'])) {
                    $user['username'] = html_entity_decode($user['username'], ENT_QUOTES, 'UTF-8');
                }
                if (isset($user['email'])) {
                    $user['email'] = html_entity_decode($user['email'], ENT_QUOTES, 'UTF-8');
                }
                return $user;
            }, $users);
            echo json_encode($decodedUsers, JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS);
        ?>;
        
        function showAddModal() {
            document.getElementById('modalTitle').textContent = 'Add User';
            document.getElementById('formAction').value = 'create';
            document.getElementById('submitText').textContent = 'Create User';
            document.getElementById('password').required = true;
            document.getElementById('passwordOptional').classList.add('hidden');
            document.getElementById('userForm').reset();
            document.getElementById('userModal').classList.remove('hidden');
        }
        
        function editUser(id) {
            const user = users.find(u => u.user_id == id);
            if (user) {
                document.getElementById('modalTitle').textContent = 'Edit User';
                document.getElementById('formAction').value = 'update';
                document.getElementById('submitText').textContent = 'Update User';
                document.getElementById('password').required = false;
                document.getElementById('passwordOptional').classList.remove('hidden');
                document.getElementById('userId').value = user.user_id;
                document.getElementById('username').value = user.username;
                document.getElementById('email').value = user.email;
                document.getElementById('role').value = user.role;
                document.getElementById('password').value = '';
                document.getElementById('userModal').classList.remove('hidden');
            }
        }
        
        function deleteUser(id) {
            if (confirm('Are you sure you want to delete this user?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeModal() {
            document.getElementById('userModal').classList.add('hidden');
        }
    </script>
</body>
</html>