@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===================================
   MELEVA TOURS - CUSTOM STYLES
   Custom styles that extend Tailwind
   =================================== */

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body, html {
    font-family: 'Segoe UI', sans-serif;
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Navigation Components */
  .nav-link {
    @apply text-white hover:text-orange-400 font-medium px-3 py-2 transition-all duration-300;
  }
  
  .nav-link.active {
    @apply text-orange-400 border-b-2 border-orange-400;
  }

  /* Button Components */
  .btn-primary {
    @apply bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply border-2 border-white text-white hover:bg-white hover:text-orange-500 px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg;
  }

  /* Card Components */
  .destination-card {
    @apply transition-all duration-300 border-l-4 border-transparent hover:transform hover:-translate-y-1 hover:shadow-meleva-lg hover:border-l-orange-500;
  }
  
  .package-card {
    @apply bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 hover:shadow-meleva-lg hover:transform hover:-translate-y-1;
  }

  /* Form Components */
  .form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200;
  }
  
  .form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200 resize-vertical;
  }

  /* Hero Section */
  .hero-section {
    @apply relative bg-cover bg-center bg-no-repeat;
    height: 80vh;
  }
  
  .hero-section.standard {
    height: 50vh;
  }
  
  .hero-overlay {
    @apply absolute inset-0 bg-black bg-opacity-50;
  }

  /* Text Shadow Utility */
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .text-shadow-lg {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  }
}

@layer utilities {
  /* Custom utilities */
  .gradient-bg {
    background: linear-gradient(135deg, #7c2d12, #92400e);
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
  }
  
  /* Navigation specific utilities */
  .nav-visible {
    transform: translateY(0);
    opacity: 1;
  }
  
  .nav-hidden {
    transform: translateY(-100%);
    opacity: 0;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
}

/* ===================================
   LEGACY COMPATIBILITY
   Maintaining existing custom styles
   =================================== */

/* Ensure hero sections start from top when nav is transparent */
body.has-hero-section {
  padding-top: 0;
}

/* Add padding for pages without hero sections */
body:not(.has-hero-section) {
  padding-top: 80px;
}

/* Navigation transition */
#main-nav {
  transition: all 0.3s ease-in-out;
}

/* Mega menu styles */
.mega-menu {
  @apply absolute top-full left-0 w-full bg-white shadow-lg border-t-4 border-orange-500 opacity-0 invisible transform translate-y-2 transition-all duration-300 ease-out z-40;
}

.mega-menu.show {
  @apply opacity-100 visible transform translate-y-0;
}

/* Mobile menu styles */
.mobile-menu {
  @apply fixed top-0 left-0 w-full h-full bg-white transform -translate-x-full transition-transform duration-300 ease-in-out z-50;
}

.mobile-menu.show {
  @apply transform translate-x-0;
}

/* Swiper customizations */
.swiper-button-next,
.swiper-button-prev {
  @apply text-orange-500 hover:text-orange-600 transition-colors duration-200;
}

.swiper-pagination-bullet {
  @apply bg-gray-400 opacity-50;
}

.swiper-pagination-bullet-active {
  @apply bg-orange-500 opacity-100;
}

/* Custom navigation buttons for featured packages */
.swiper-button-next-custom,
.swiper-button-prev-custom {
  @apply w-14 h-14 bg-gray-900 hover:bg-gray-800 rounded-full shadow-lg hover:shadow-xl;
  @apply flex items-center justify-center text-orange-500 hover:text-orange-400;
  @apply transition-all duration-300 cursor-pointer select-none;
  @apply absolute top-1/2 -translate-y-1/2 z-20;
  border: none;
  outline: none;
}

.swiper-button-prev-custom {
  @apply -left-6 -translate-x-6;
}

.swiper-button-next-custom {
  @apply -right-6 translate-x-6;
}

/* Hide default Swiper navigation */
.swiper-button-next:not(.swiper-button-next-custom),
.swiper-button-prev:not(.swiper-button-prev-custom) {
  display: none !important;
}

/* Payment card styles */
.payment-card {
  background: #FFE5B4;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  border: 1px solid #FFE5B4;
}

/* Admin dashboard styles */
.sidebar-transition {
  transition: all 0.3s ease;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
