-- <PERSON><PERSON>t to drop all tables from Meleva Tours database
-- WARNING: This will permanently delete all data!
-- Make sure you have a backup before running this!

USE your_live_database_name;

-- Disable foreign key checks to avoid constraint errors
SET FOREIGN_KEY_CHECKS = 0;

-- Drop all tables in the correct order to handle dependencies
DROP TABLE IF EXISTS payment_notifications;
DROP TABLE IF EXISTS payment_links;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS booking_packages;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS quote_history;
DROP TABLE IF EXISTS quote_packages;
DROP TABLE IF EXISTS quotes;
DROP TABLE IF EXISTS reviews;
DROP TABLE IF EXISTS reports;
DROP TABLE IF EXISTS email_tracking;
DROP TABLE IF EXISTS email_conversations;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS contact_info;
DROP TABLE IF EXISTS tour_packages;
DROP TABLE IF EXISTS destinations;
DROP TABLE IF EXISTS images;
DROP TABLE IF EXISTS tour_package_types;
DROP TABLE IF EXISTS gallery;
DROP TABLE IF EXISTS password_resets;
DROP TABLE IF EXISTS users;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify all tables are dropped
SHOW TABLES;
