<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

// Function to determine the back navigation
function getBackNavigation() {
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $currentHost = $_SERVER['HTTP_HOST'] ?? '';

    // Check for explicit back parameter in URL (for header dropdown navigation)
    if (isset($_GET['from'])) {
        switch ($_GET['from']) {
            case 'tours':
                return [
                    'url' => 'tours.php#destinations',
                    'text' => 'Back to Tours & Destinations'
                ];
            case 'index':
                return [
                    'url' => 'index.php#destinations',
                    'text' => 'Back to Popular Destinations'
                ];
        }
    }

    // Check if referer is from the same domain
    if (!empty($referer) && strpos($referer, $currentHost) !== false) {
        $refererPath = parse_url($referer, PHP_URL_PATH);
        $refererFile = basename($refererPath);
        $refererQuery = parse_url($referer, PHP_URL_QUERY);

        // Determine back link based on referring page
        switch ($refererFile) {
            case 'tours.php':
                $anchor = '';
                // Check if there were filters applied
                if (!empty($refererQuery)) {
                    $anchor = '?' . $refererQuery . '#destinations';
                } else {
                    $anchor = '#destinations';
                }
                return [
                    'url' => 'tours.php' . $anchor,
                    'text' => 'Back to Tours & Destinations'
                ];
            case 'index.php':
                return [
                    'url' => 'index.php#destinations',
                    'text' => 'Back to Popular Destinations'
                ];
            default:
                // If coming from any other page on the same domain, go back to that page
                if (strpos($referer, 'destination-details.php') === false) {
                    return [
                        'url' => $referer,
                        'text' => 'Back to Previous Page'
                    ];
                }
        }
    }

    // Default fallback - prioritize tours page over index
    return [
        'url' => 'tours.php#destinations',
        'text' => 'Back to Tours & Destinations'
    ];
}

// Get back navigation details
$backNav = getBackNavigation();

// Get destination ID from URL
$destinationId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($destinationId <= 0) {
    header('Location: index.php');
    exit;
}

// Initialize models
$destinationModel = new Destination();
$tourPackageModel = new TourPackage();
$imageModel = new Image();

// Fetch destination details
$destination = $destinationModel->findById($destinationId);

if (!$destination) {
    header('Location: index.php');
    exit;
}

// Fetch all images for this destination
$images = $imageModel->findByDestination($destinationId);

// Fetch tour packages for this destination
$destinationPackages = $tourPackageModel->findByDestination($destinationId);

// Set SEO data for destination details page
$seoTitle = htmlspecialchars($destination['name']) . ' Safari Destination';
$seoDescription = htmlspecialchars($destination['short_description'] ?? 'Discover ' . $destination['name'] . ' with Meleva Tours & Travel. Experience authentic African safari adventures in this stunning destination.');
$seoKeywords = htmlspecialchars($destination['name']) . ', Kenya safari, African tours, wildlife safari, ' . htmlspecialchars($destination['name']) . ' tours, Kenya destinations, safari packages';
$seoImage = !empty($destination['display_image_url']) ? 'admin-dashboard/' . $destination['display_image_url'] : 'images/hero-bg.jpg';
$seoType = 'article';

// Initialize breadcrumbs
require_once 'includes/breadcrumbs.php';
$breadcrumbs = new Breadcrumbs();
$breadcrumbs->addBreadcrumb('Tours & Destinations', $breadcrumbs->getBaseUrl() . '/tours.php');
$breadcrumbs->addBreadcrumb($destination['name']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="dist/tailwind.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css" />
    <style>
        .gradient-text {
            background: linear-gradient(135deg, #f97316, #ea580c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Tab Styles */
        .tab-button.active {
            color: #ea580c !important;
            border-color: #ea580c !important;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Swiper Container */
        .swiper-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 1rem;
            overflow: hidden;
            background: #ccc;
        }

        /* Larger height for desktop */
        @media (min-width: 768px) {
            .swiper-container {
                height: 500px;
            }
        }

        /* Slide Image */
        .swiper-slide img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* Navigation Buttons */
        .swiper-button-prev,
        .swiper-button-next {
            background: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: none; /* Hidden by default */
        }

        .swiper-button-prev:hover,
        .swiper-button-next:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .swiper-button-prev::after,
        .swiper-button-next::after {
            color: white;
            font-size: 20px;
        }

        /* Show navigation on larger screens */
        @media (min-width: 768px) {
            .swiper-button-prev,
            .swiper-button-next {
                display: flex;
            }
        }

        /* Pagination Styles */
        .swiper-pagination {
            position: absolute !important;
            bottom: 20px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            z-index: 20 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            width: auto !important;
        }

        .swiper-pagination-bullet {
            width: 12px !important;
            height: 12px !important;
            background: rgba(255, 255, 255, 0.7) !important;
            opacity: 1 !important;
            margin: 0 6px !important;
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
        }

        .swiper-pagination-bullet-active {
            width: 24px !important;
            height: 12px !important;
            background: #f97316 !important;
            border-radius: 6px !important;
            border: 2px solid rgba(255, 255, 255, 0.8) !important;
        }

        /* No Images Placeholder */
        .no-images-placeholder {
            width: 100%;
            height: 300px;
            background: #f3f4f6;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Destination Details -->
    <section class="py-20 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Breadcrumbs -->
            <?php echo $breadcrumbs->generateBreadcrumbs(); ?>

            <!-- Back Button -->
            <div class="mb-8">
                <a href="<?php echo htmlspecialchars($backNav['url']); ?>" class="inline-flex items-center text-orange-500 hover:text-orange-600 font-medium">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                    <?php echo htmlspecialchars($backNav['text']); ?>
                </a>
            </div>

            <!-- Destination Header -->
            <div class="text-center mb-12">
                <h1 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    <?php echo Utils::displayText($destination['name']); ?>
                </h1>

                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                    <?php echo Utils::displayText($destination['short_description'] ?: 'Discover this amazing destination with unique experiences.'); ?>
                </p>
            </div>

            <!-- Tab Navigation -->
            <div class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex justify-center space-x-8">
                        <button id="description-tab" class="tab-button active py-4 px-6 border-b-2 border-orange-500 font-medium text-orange-600 whitespace-nowrap">
                            <i class="fas fa-info-circle mr-2"></i>
                            Description
                        </button>
                        <button id="packages-tab" class="tab-button py-4 px-6 border-b-2 border-transparent font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap">
                            <i class="fas fa-suitcase-rolling mr-2"></i>
                            View Packages
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="mb-16">
                <!-- Description Tab Content -->
                <div id="description-content" class="tab-content active">
                    <!-- Main Content Grid (3/4 Images + 1/4 Details) -->
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-12 mb-16">
                        <!-- Images Section with Swiper (3/4 width) -->
                        <div class="lg:col-span-3">
                            <?php if (!empty($images)): ?>
                                <div class="swiper-container">
                                    <div class="swiper-wrapper">
                                        <?php foreach ($images as $index => $image): ?>
                                            <div class="swiper-slide">
                                                <img src="admin-dashboard/<?php echo htmlspecialchars($image['url']); ?>"
                                                     alt="<?php echo htmlspecialchars($image['alt_text'] ?: $destination['name']); ?>"
                                                     loading="lazy">
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <!-- Navigation buttons -->
                                    <div class="swiper-button-prev"></div>
                                    <div class="swiper-button-next"></div>
                                    <!-- Pagination -->
                                    <div class="swiper-pagination"></div>
                                </div>
                            <?php else: ?>
                                <div class="no-images-placeholder">
                                    <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p>No Images Available</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Details Section (1/4 width) -->
                        <div class="lg:col-span-1">
                            <div class="bg-white rounded-2xl shadow-lg p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-6">Destination Details</h2>

                                <div class="space-y-4 mb-8">
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                        <span class="font-medium text-gray-600">Starting Price:</span>
                                        <span class="text-xl font-bold gradient-text">
                                            $<?php echo number_format($destination['price']); ?>
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                        <span class="font-medium text-gray-600">Location:</span>
                                        <span class="text-md font-semibold text-gray-900">
                                            <?php echo Utils::displayText($destination['location'] ?: 'Not specified'); ?>
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                        <span class="font-medium text-gray-600">Photos:</span>
                                        <span class="text-md font-semibold text-gray-900">
                                            <?php echo count($images); ?> Available
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200">
                                        <span class="font-medium text-gray-600">Tour Packages:</span>
                                        <span class="text-md font-semibold text-gray-900">
                                            <?php echo count($destinationPackages); ?> Available
                                        </span>
                                    </div>
                                </div>

                                <!-- Quote CTA -->
                                <div class="text-center space-y-3">
                                    <a href="request-quote.php?destination_id=<?php echo $destinationId; ?>"
                                       class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105 inline-block w-full">
                                        Get Custom Quote
                                    </a>
                                    <a href="contact.php"
                                       class="border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white px-6 py-2 rounded-full font-medium transition duration-300 inline-block w-full text-sm">
                                        Ask Questions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Packages Tab Content -->
                <div id="packages-content" class="tab-content">
                    <?php if (!empty($destinationPackages)): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <?php foreach ($destinationPackages as $package): ?>
                                <div class="bg-gray-900 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                    <?php if ($package['display_image_url']): ?>
                                        <img src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                             alt="<?php echo htmlspecialchars($package['name']); ?>"
                                             class="w-full h-48 object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                            <div class="text-center text-white">
                                                <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p class="text-sm">Coming Soon</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="relative pt-4 p-6 h-48">
                                        <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                            <?php
                                            // Smart formatting based on package type and duration
                                            $packageName = $package['name'];
                                            $tourType = $package['type_name'] ?: 'Tour';
                                            $duration = $package['duration'];

                                            // Check if tour type already implies duration
                                            $lowerTourType = strtolower($tourType);
                                            $typeImpliesDuration = strpos($lowerTourType, 'day') !== false ||
                                                                  strpos($lowerTourType, 'overnight') !== false ||
                                                                  strpos($lowerTourType, 'night') !== false;

                                            if ($typeImpliesDuration) {
                                                // Format: Tour Type + Package Name
                                                $formattedName = $tourType . ' ' . $packageName;
                                            } elseif (!empty($duration)) {
                                                // Format: Duration + Package Name + Tour Type
                                                $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                            } else {
                                                // Format: Package Name + Tour Type
                                                $formattedName = $packageName . ' ' . $tourType;
                                            }

                                            echo Utils::displayText($formattedName);
                                            ?>
                                        </h3>
                                        <p class="text-gray-200 mb-4 text-base line-clamp-2">
                                            <?php
                                            $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                            echo Utils::displayText($description);
                                            ?>
                                        </p>
                                        <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                            <span class="text-base font-semibold text-orange-400">
                                                $<?php echo number_format($package['price']); ?>
                                            </span>
                                            <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>&from=destination"
                                               class="text-sm bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full font-medium transition duration-300 flex items-center">
                                                View Details <i class="fas fa-arrow-right ml-2"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-16">
                            <div class="max-w-md mx-auto">
                                <svg class="w-24 h-24 mx-auto mb-6 text-gray-400" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                                <h3 class="text-xl font-bold text-gray-900 mb-2">No Packages Available</h3>
                                <p class="text-gray-600 mb-6">There are currently no tour packages available for this destination. Check back soon for exciting new packages!</p>
                                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                    <a href="request-quote.php?destination_id=<?php echo $destinationId; ?>" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                        Request Custom Quote
                                    </a>
                                    <a href="contact.php" class="border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                        Contact Us
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Destinations -->
            <?php
            require_once 'includes/internal-links.php';
            $internalLinks = new InternalLinks();
            echo $internalLinks->generateRelatedLinksSection('destinations', $destinationId);
            ?>

        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <script>
        // Global Swiper variable
        let destinationSwiper = null;

        // Function to initialize Swiper
        function initializeSwiper() {
            <?php if (!empty($images)): ?>
            if (destinationSwiper) {
                destinationSwiper.destroy(true, true);
            }

            setTimeout(function() {
                destinationSwiper = new Swiper('.swiper-container', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        type: 'bullets',
                        dynamicBullets: false,
                        renderBullet: function (index, className) {
                            return '<span class="' + className + '"></span>';
                        },
                    },
                    keyboard: {
                        enabled: true,
                    },
                    breakpoints: {
                        320: {
                            navigation: {
                                enabled: false
                            }
                        },
                        768: {
                            navigation: {
                                enabled: true
                            }
                        }
                    }
                });
            }, 100);
            <?php endif; ?>
        }

        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.id.replace('-tab', '-content');

                    // Remove active class from all tabs and contents
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.add('text-gray-500', 'border-transparent');
                        btn.classList.remove('text-orange-600', 'border-orange-500');
                    });

                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    this.classList.remove('text-gray-500', 'border-transparent');
                    this.classList.add('text-orange-600', 'border-orange-500');

                    document.getElementById(targetTab).classList.add('active');

                    // Reinitialize Swiper when switching to Description tab
                    if (targetTab === 'description-content') {
                        initializeSwiper();
                    }
                });
            });

            // Initialize Swiper on page load
            initializeSwiper();
        });
    </script>
    <script src="js/global.js"></script>

    <!-- Structured Data for Destination -->
    <script type="application/ld+json">
    <?php
    require_once 'includes/seo-meta.php';
    $seoMeta = new SEOMeta();
    echo $seoMeta->generateStructuredData([
        'type' => 'Destination',
        'name' => $destination['name'],
        'description' => $destination['short_description'] ?? $destination['description'] ?? '',
        'image' => !empty($destination['display_image_url']) ? $seoMeta->getBaseUrl() . '/admin-dashboard/' . $destination['display_image_url'] : '',
        'url' => $seoMeta->getBaseUrl() . '/destination-details.php?id=' . $destination['destination_id'],
        'latitude' => $destination['latitude'] ?? '',
        'longitude' => $destination['longitude'] ?? ''
    ]);
    ?>
    </script>

    <!-- Breadcrumb Structured Data -->
    <?php echo $breadcrumbs->generateStructuredData(); ?>

    <?php include 'footer.php'; ?>

</body>
</html>
